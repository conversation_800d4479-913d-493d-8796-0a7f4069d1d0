# CMakeLists.txt
cmake_minimum_required(VERSION 3.16)
project(OptimizedPdfViewer
    VERSION 1.0.0
    DESCRIPTION "High-performance PDF viewer with advanced annotation capabilities"
    LANGUAGES CXX
)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Build type
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release CACHE STRING "Choose the type of build" FORCE)
    set_property(CACHE CMAKE_BUILD_TYPE PROPERTY STRINGS "Debug" "Release" "MinSizeRel" "RelWithDebInfo")
endif()

# Compiler-specific options
if(MSVC)
    add_compile_options(/W4)
    add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
else()
    add_compile_options(-Wall -Wextra -Wpedantic)
    if(CMAKE_BUILD_TYPE STREQUAL "Debug")
        add_compile_options(-g -O0)
    endif()
endif()

# Find Qt6 and its components
find_package(Qt6 REQUIRED COMPONENTS Core Gui Widgets Concurrent PrintSupport)

# Find PkgConfig to locate Poppler
find_package(PkgConfig REQUIRED)
pkg_check_modules(POPPLER_CPP REQUIRED IMPORTED_TARGET poppler-cpp)
pkg_check_modules(POPPLER_QT6 REQUIRED IMPORTED_TARGET poppler-qt6)

# Add cpptrace for comprehensive crash handling and stack traces (temporarily disabled)
# include(FetchContent)
# FetchContent_Declare(
#     cpptrace
#     GIT_REPOSITORY https://github.com/jeremy-rifkin/cpptrace.git
#     GIT_TAG        v1.0.3 # Latest stable version
# )
# FetchContent_MakeAvailable(cpptrace)

# Add ElaWidgetTools subdirectory from lib directory
if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/lib/ElaWidgetTools/CMakeLists.txt")
    add_subdirectory(lib/ElaWidgetTools)
    set(ELA_WIDGETS_AVAILABLE ON)
    message(STATUS "ElaWidgetTools found in lib directory and will be integrated")
else()
    set(ELA_WIDGETS_AVAILABLE OFF)
    message(WARNING "ElaWidgetTools not found in lib directory.")
endif()

# Qt settings
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

# Source files
set(SOURCES
    src/main.cpp
    src/MainWindow.cpp
    src/PdfController.cpp
    src/SettingsDialog.cpp
    src/DocumentTab.cpp
    src/Annotation.cpp
    src/AnnotationManager.cpp
    src/AnnotationToolbar.cpp
    src/AnnotationOverlay.cpp
    src/AnnotationCommand.cpp
    src/AnnotationPropertiesDialog.cpp
    src/AnnotationClipboard.cpp
    src/AnnotationSearchDialog.cpp
    src/DocumentInfoDialog.cpp
    src/SearchResultsPanel.cpp
    src/PrintPreviewDialog.cpp
    src/BookmarkDialog.cpp
    src/FullScreenOverlay.cpp
    src/ElaIntegration.cpp
    src/WelcomeScreen.cpp
    src/LoadingOverlay.cpp
    src/ModernContextMenu.cpp
    src/RichTooltip.cpp
    src/ThemeCustomizer.cpp
    src/Logger.cpp
    src/RibbonInterface.cpp
    src/ErrorHandler.cpp
    src/CrashLogPackager.cpp
    src/OnboardingTour.cpp
    src/DesignSystem.cpp
    src/TextSelectionOverlay.cpp
    src/AdvancedZoomManager.cpp
    src/SearchHistory.cpp
    src/SearchWidget.cpp
)

# Header files
set(HEADERS
    include/MainWindow.h
    include/PdfController.h
    include/SettingsDialog.h
    include/DocumentTab.h
    include/Annotation.h
    include/AnnotationManager.h
    include/AnnotationToolbar.h
    include/AnnotationOverlay.h
    include/AnnotationCommand.h
    include/AnnotationPropertiesDialog.h
    include/AnnotationClipboard.h
    include/AnnotationSearchDialog.h
    include/DocumentInfoDialog.h
    include/SearchResultsPanel.h
    include/PrintPreviewDialog.h
    include/BookmarkDialog.h
    include/FullScreenOverlay.h
    include/ElaIntegration.h
    include/WelcomeScreen.h
    include/LoadingOverlay.h
    include/ModernContextMenu.h
    include/RichTooltip.h
    include/OnboardingTour.h
    include/ThemeCustomizer.h
    include/Logger.h
    include/ErrorHandler.h
    include/CrashLogPackager.h
    include/RibbonInterface.h
    include/TextSelectionOverlay.h
    include/AdvancedZoomManager.h
    include/SearchHistory.h
    include/SearchWidget.h
)

# Create executable
add_executable(optimized-pdf-viewer ${SOURCES} ${HEADERS})

# Set target properties
set_target_properties(optimized-pdf-viewer PROPERTIES
    OUTPUT_NAME "optimized-pdf-viewer"
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_MAJOR}
)

# Include directories for the target
target_include_directories(optimized-pdf-viewer PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_BINARY_DIR}
)

# Include ElaWidgetTools headers if available
if(ELA_WIDGETS_AVAILABLE)
    target_include_directories(optimized-pdf-viewer PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}/lib/ElaWidgetTools/include
    )
endif()

# Link libraries
target_link_libraries(optimized-pdf-viewer PRIVATE
    Qt6::Core
    Qt6::Gui
    Qt6::Widgets
    Qt6::Concurrent
    Qt6::PrintSupport
    PkgConfig::POPPLER_CPP
    PkgConfig::POPPLER_QT6
    # cpptrace::cpptrace # Temporarily disabled
)

# Link ElaWidgetTools if available
if(ELA_WIDGETS_AVAILABLE)
    target_link_libraries(optimized-pdf-viewer PRIVATE ElaWidgetTools)
    target_compile_definitions(optimized-pdf-viewer PRIVATE ELA_WIDGETS_ENABLED)
endif()

# Copy cpptrace.dll on Windows for runtime (temporarily disabled)
# if(WIN32)
#     add_custom_command(
#         TARGET optimized-pdf-viewer POST_BUILD
#         COMMAND ${CMAKE_COMMAND} -E copy_if_different
#         $<TARGET_FILE:cpptrace::cpptrace>
#         $<TARGET_FILE_DIR:optimized-pdf-viewer>
#     )
# endif()

# Compile definitions
target_compile_definitions(optimized-pdf-viewer PRIVATE
    QT_DISABLE_DEPRECATED_BEFORE=0x060000
    $<$<CONFIG:Debug>:QT_QML_DEBUG>
)

# Installation
include(GNUInstallDirs)

install(TARGETS optimized-pdf-viewer
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
)

# Install documentation
install(FILES README.md LICENSE CONTRIBUTING.md
    DESTINATION ${CMAKE_INSTALL_DOCDIR}
)

# Create desktop entry on Linux
if(UNIX AND NOT APPLE)
    configure_file(
        ${CMAKE_CURRENT_SOURCE_DIR}/scripts/optimized-pdf-viewer.desktop.in
        ${CMAKE_CURRENT_BINARY_DIR}/optimized-pdf-viewer.desktop
        @ONLY
    )
    install(FILES ${CMAKE_CURRENT_BINARY_DIR}/optimized-pdf-viewer.desktop
        DESTINATION ${CMAKE_INSTALL_DATADIR}/applications
    )
endif()

# Enable testing and add tests subdirectory
option(BUILD_TESTS "Build unit tests" ON)
if(BUILD_TESTS)
    enable_testing()
    add_subdirectory(tests)
endif()

# Development tools
option(ENABLE_CLANG_TIDY "Enable clang-tidy checks" OFF)
if(ENABLE_CLANG_TIDY)
    find_program(CLANG_TIDY_EXE NAMES "clang-tidy")
    if(CLANG_TIDY_EXE)
        set_target_properties(optimized-pdf-viewer PROPERTIES
            CXX_CLANG_TIDY "${CLANG_TIDY_EXE}"
        )
    endif()
endif()

# Documentation generation with Doxygen
option(BUILD_DOCUMENTATION "Build API documentation with Doxygen" ON)
if(BUILD_DOCUMENTATION)
    find_package(Doxygen QUIET)
    if(DOXYGEN_FOUND)
        # Configure Doxygen
        set(DOXYGEN_PROJECT_NAME "Optimized PDF Viewer")
        set(DOXYGEN_PROJECT_NUMBER ${PROJECT_VERSION})
        set(DOXYGEN_PROJECT_BRIEF "High-performance PDF viewer with advanced annotation capabilities")
        set(DOXYGEN_OUTPUT_DIRECTORY "${CMAKE_CURRENT_BINARY_DIR}/docs")
        set(DOXYGEN_INPUT_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}/include ${CMAKE_CURRENT_SOURCE_DIR}/src")
        set(DOXYGEN_RECURSIVE YES)
        set(DOXYGEN_EXTRACT_ALL YES)
        set(DOXYGEN_EXTRACT_PRIVATE NO)
        set(DOXYGEN_EXTRACT_STATIC YES)
        set(DOXYGEN_GENERATE_HTML YES)
        set(DOXYGEN_GENERATE_LATEX NO)
        set(DOXYGEN_GENERATE_XML YES)
        set(DOXYGEN_HTML_OUTPUT "html")
        set(DOXYGEN_XML_OUTPUT "xml")
        set(DOXYGEN_USE_MDFILE_AS_MAINPAGE "${CMAKE_CURRENT_SOURCE_DIR}/README.md")
        set(DOXYGEN_MARKDOWN_SUPPORT YES)
        set(DOXYGEN_AUTOLINK_SUPPORT YES)
        set(DOXYGEN_BUILTIN_STL_SUPPORT YES)
        set(DOXYGEN_JAVADOC_AUTOBRIEF YES)
        set(DOXYGEN_QT_AUTOBRIEF YES)
        set(DOXYGEN_MULTILINE_CPP_IS_BRIEF YES)
        set(DOXYGEN_INHERIT_DOCS YES)
        set(DOXYGEN_SEPARATE_MEMBER_PAGES NO)
        set(DOXYGEN_TAB_SIZE 4)
        set(DOXYGEN_ALIASES
            "rst=\\verbatim embed:rst:leading-asterisk"
            "endrst=\\endverbatim"
        )
        set(DOXYGEN_EXAMPLE_PATH "${CMAKE_CURRENT_SOURCE_DIR}/docs/examples")
        set(DOXYGEN_IMAGE_PATH "${CMAKE_CURRENT_SOURCE_DIR}/docs/images")
        set(DOXYGEN_STRIP_FROM_PATH "${CMAKE_CURRENT_SOURCE_DIR}")
        set(DOXYGEN_STRIP_FROM_INC_PATH "${CMAKE_CURRENT_SOURCE_DIR}/include")
        set(DOXYGEN_SOURCE_BROWSER YES)
        set(DOXYGEN_INLINE_SOURCES NO)
        set(DOXYGEN_VERBATIM_HEADERS YES)
        set(DOXYGEN_CLANG_ASSISTED_PARSING YES)
        set(DOXYGEN_CLANG_OPTIONS "-std=c++17")

        # HTML theme configuration
        set(DOXYGEN_HTML_THEME "doxygen-awesome-css")
        set(DOXYGEN_HTML_EXTRA_STYLESHEET
            "${CMAKE_CURRENT_SOURCE_DIR}/docs/doxygen-theme/doxygen-awesome.css"
            "${CMAKE_CURRENT_SOURCE_DIR}/docs/doxygen-theme/doxygen-awesome-sidebar-only.css"
            "${CMAKE_CURRENT_SOURCE_DIR}/docs/doxygen-theme/custom.css"
        )
        set(DOXYGEN_HTML_EXTRA_FILES
            "${CMAKE_CURRENT_SOURCE_DIR}/docs/doxygen-theme/doxygen-awesome-darkmode-toggle.js"
            "${CMAKE_CURRENT_SOURCE_DIR}/docs/doxygen-theme/doxygen-awesome-fragment-copy-button.js"
            "${CMAKE_CURRENT_SOURCE_DIR}/docs/doxygen-theme/doxygen-awesome-paragraph-link.js"
        )
        set(DOXYGEN_HTML_HEADER "${CMAKE_CURRENT_SOURCE_DIR}/docs/doxygen-theme/header.html")
        set(DOXYGEN_HTML_FOOTER "${CMAKE_CURRENT_SOURCE_DIR}/docs/doxygen-theme/footer.html")
        set(DOXYGEN_HTML_COLORSTYLE_HUE 220)
        set(DOXYGEN_HTML_COLORSTYLE_SAT 100)
        set(DOXYGEN_HTML_COLORSTYLE_GAMMA 80)
        set(DOXYGEN_HTML_TIMESTAMP YES)
        set(DOXYGEN_HTML_DYNAMIC_SECTIONS YES)
        set(DOXYGEN_HTML_INDEX_NUM_ENTRIES 100)

        # Search configuration
        set(DOXYGEN_SEARCHENGINE YES)
        set(DOXYGEN_SERVER_BASED_SEARCH NO)

        # Diagram generation
        set(DOXYGEN_HAVE_DOT YES)
        set(DOXYGEN_CLASS_DIAGRAMS YES)
        set(DOXYGEN_COLLABORATION_GRAPH YES)
        set(DOXYGEN_GROUP_GRAPHS YES)
        set(DOXYGEN_UML_LOOK YES)
        set(DOXYGEN_UML_LIMIT_NUM_FIELDS 50)
        set(DOXYGEN_TEMPLATE_RELATIONS YES)
        set(DOXYGEN_INCLUDE_GRAPH YES)
        set(DOXYGEN_INCLUDED_BY_GRAPH YES)
        set(DOXYGEN_CALL_GRAPH NO)
        set(DOXYGEN_CALLER_GRAPH NO)
        set(DOXYGEN_GRAPHICAL_HIERARCHY YES)
        set(DOXYGEN_DIRECTORY_GRAPH YES)
        set(DOXYGEN_DOT_IMAGE_FORMAT svg)
        set(DOXYGEN_INTERACTIVE_SVG YES)
        set(DOXYGEN_DOT_GRAPH_MAX_NODES 50)
        set(DOXYGEN_DOT_TRANSPARENT YES)

        # Create the documentation target
        doxygen_add_docs(docs
            ${CMAKE_CURRENT_SOURCE_DIR}/include
            ${CMAKE_CURRENT_SOURCE_DIR}/src
            ${CMAKE_CURRENT_SOURCE_DIR}/README.md
            ${CMAKE_CURRENT_SOURCE_DIR}/docs
            WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
            COMMENT "Generating API documentation with Doxygen"
        )

        # Install documentation
        install(DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/docs/html
            DESTINATION ${CMAKE_INSTALL_DOCDIR}/api
            OPTIONAL
        )

        message(STATUS "Doxygen found: API documentation will be generated")
        message(STATUS "Run 'make docs' or 'cmake --build . --target docs' to generate documentation")
    else()
        message(WARNING "Doxygen not found: API documentation will not be generated")
    endif()
endif()

# Include comprehensive documentation configuration
include(docs/config/documentation.cmake)

# Include comprehensive documentation configuration
include(docs/config/documentation.cmake)

# CPack configuration
set(CPACK_PACKAGE_NAME "OptimizedPdfViewer")
set(CPACK_PACKAGE_VENDOR "PDF Viewer Team")
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY "High-performance PDF viewer with annotation support")
set(CPACK_PACKAGE_VERSION ${PROJECT_VERSION})
set(CPACK_PACKAGE_INSTALL_DIRECTORY "OptimizedPdfViewer")
set(CPACK_RESOURCE_FILE_LICENSE "${CMAKE_CURRENT_SOURCE_DIR}/LICENSE")
set(CPACK_RESOURCE_FILE_README "${CMAKE_CURRENT_SOURCE_DIR}/README.md")

if(WIN32)
    set(CPACK_GENERATOR "NSIS;ZIP")
    set(CPACK_NSIS_DISPLAY_NAME "Optimized PDF Viewer")
    set(CPACK_NSIS_PACKAGE_NAME "OptimizedPdfViewer")
    set(CPACK_NSIS_CONTACT "<EMAIL>")
    set(CPACK_NSIS_HELP_LINK "https://github.com/example/qt-pdf-render")
elseif(APPLE)
    set(CPACK_GENERATOR "DragNDrop")
else()
    set(CPACK_GENERATOR "DEB;RPM;TGZ")
    set(CPACK_DEBIAN_PACKAGE_MAINTAINER "PDF Viewer Team")
    set(CPACK_DEBIAN_PACKAGE_DEPENDS "libqt6core6, libqt6gui6, libqt6widgets6, libpoppler-qt6-3")
    set(CPACK_RPM_PACKAGE_REQUIRES "qt6-qtbase, poppler-qt6")
endif()

include(CPack)
